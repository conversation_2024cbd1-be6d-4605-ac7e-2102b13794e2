import 'user_model.dart';

/// نموذج الصلاحية
class Permission {
  final int id;
  final String name;
  final String? description;
  final String category; // system, task, user, etc.
  final String action; // create, read, update, delete, etc.
  final String? resource; // tasks, users, reports, etc.
  final bool isActive;
  final int createdAt;
  final int? updatedAt;

  const Permission({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.action,
    this.resource,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    return Permission(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: json['category'] as String,
      action: json['action'] as String,
      resource: json['resource'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'action': action,
      'resource': resource,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Permission copyWith({
    int? id,
    String? name,
    String? description,
    String? category,
    String? action,
    String? resource,
    bool? isActive,
    int? createdAt,
    int? updatedAt,
  }) {
    return Permission(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      action: action ?? this.action,
      resource: resource ?? this.resource,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Permission(id: $id, name: $name, action: $action)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Permission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج صلاحية المستخدم
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final int? revokedAt;
  final int? revokedBy;
  final bool isActive;
  final String? notes;

  // Navigation properties
  final User? user;
  final Permission? permission;
  final User? grantedByUser;
  final User? revokedByUser;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.revokedAt,
    this.revokedBy,
    this.isActive = true,
    this.notes,
    this.user,
    this.permission,
    this.grantedByUser,
    this.revokedByUser,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as int,
      userId: json['userId'] as int,
      permissionId: json['permissionId'] as int,
      grantedBy: json['grantedBy'] as int,
      grantedAt: json['grantedAt'] as int,
      revokedAt: json['revokedAt'] as int?,
      revokedBy: json['revokedBy'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      notes: json['notes'] as String?,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      permission: json['permission'] != null 
          ? Permission.fromJson(json['permission'] as Map<String, dynamic>)
          : null,
      grantedByUser: json['grantedByNavigation'] != null 
          ? User.fromJson(json['grantedByNavigation'] as Map<String, dynamic>)
          : null,
      revokedByUser: json['revokedByNavigation'] != null 
          ? User.fromJson(json['revokedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'revokedAt': revokedAt,
      'revokedBy': revokedBy,
      'isActive': isActive,
      'notes': notes,
    };
  }

  UserPermission copyWith({
    int? id,
    int? userId,
    int? permissionId,
    int? grantedBy,
    int? grantedAt,
    int? revokedAt,
    int? revokedBy,
    bool? isActive,
    String? notes,
    User? user,
    Permission? permission,
    User? grantedByUser,
    User? revokedByUser,
  }) {
    return UserPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      permissionId: permissionId ?? this.permissionId,
      grantedBy: grantedBy ?? this.grantedBy,
      grantedAt: grantedAt ?? this.grantedAt,
      revokedAt: revokedAt ?? this.revokedAt,
      revokedBy: revokedBy ?? this.revokedBy,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      user: user ?? this.user,
      permission: permission ?? this.permission,
      grantedByUser: grantedByUser ?? this.grantedByUser,
      revokedByUser: revokedByUser ?? this.revokedByUser,
    );
  }

  /// الحصول على تاريخ منح الصلاحية كـ DateTime
  DateTime get grantedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(grantedAt * 1000);

  /// الحصول على تاريخ إلغاء الصلاحية كـ DateTime
  DateTime? get revokedAtDateTime => revokedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(revokedAt! * 1000)
      : null;

  /// التحقق من كون الصلاحية ملغاة
  bool get isRevoked => revokedAt != null;

  @override
  String toString() {
    return 'UserPermission(id: $id, userId: $userId, permissionId: $permissionId, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPermission && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب منح صلاحية
class GrantPermissionRequest {
  final int userId;
  final int permissionId;
  final String? notes;

  const GrantPermissionRequest({
    required this.userId,
    required this.permissionId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'permissionId': permissionId,
      'notes': notes,
    };
  }
}

/// نموذج طلب إلغاء صلاحية
class RevokePermissionRequest {
  final int userPermissionId;
  final String? notes;

  const RevokePermissionRequest({
    required this.userPermissionId,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'userPermissionId': userPermissionId,
      'notes': notes,
    };
  }
}

/// نموذج طلب إنشاء صلاحية جديدة
class CreatePermissionRequest {
  final String name;
  final String? description;
  final String category;
  final String action;
  final String? resource;

  const CreatePermissionRequest({
    required this.name,
    this.description,
    required this.category,
    required this.action,
    this.resource,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'category': category,
      'action': action,
      'resource': resource,
    };
  }
}
