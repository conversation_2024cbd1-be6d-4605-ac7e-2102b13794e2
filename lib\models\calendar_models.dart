import 'user_model.dart';
import 'task_models.dart';

/// نموذج حدث التقويم
class CalendarEvent {
  final int id;
  final String title;
  final String? description;
  final int startTime; // Unix timestamp
  final int endTime; // Unix timestamp
  final bool allDay;
  final String? location;
  final String? color;
  final int userId;
  final int? taskId;
  final String? recurrenceRule;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final int? duration; // بالدقائق

  // Navigation properties
  final User? user;
  final Task? task;

  const CalendarEvent({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.allDay = false,
    this.location,
    this.color,
    required this.userId,
    this.taskId,
    this.recurrenceRule,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.duration,
    this.user,
    this.task,
  });

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      startTime: json['startTime'] as int,
      endTime: json['endTime'] as int,
      allDay: json['allDay'] as bool? ?? false,
      location: json['location'] as String?,
      color: json['color'] as String?,
      userId: json['userId'] as int,
      taskId: json['taskId'] as int?,
      recurrenceRule: json['recurrenceRule'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      duration: json['duration'] as int?,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      task: json['task'] != null 
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startTime': startTime,
      'endTime': endTime,
      'allDay': allDay,
      'location': location,
      'color': color,
      'userId': userId,
      'taskId': taskId,
      'recurrenceRule': recurrenceRule,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'duration': duration,
    };
  }

  CalendarEvent copyWith({
    int? id,
    String? title,
    String? description,
    int? startTime,
    int? endTime,
    bool? allDay,
    String? location,
    String? color,
    int? userId,
    int? taskId,
    String? recurrenceRule,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    int? duration,
    User? user,
    Task? task,
  }) {
    return CalendarEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      allDay: allDay ?? this.allDay,
      location: location ?? this.location,
      color: color ?? this.color,
      userId: userId ?? this.userId,
      taskId: taskId ?? this.taskId,
      recurrenceRule: recurrenceRule ?? this.recurrenceRule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      duration: duration ?? this.duration,
      user: user ?? this.user,
      task: task ?? this.task,
    );
  }

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime get startDateTime => 
      DateTime.fromMillisecondsSinceEpoch(startTime * 1000);

  /// الحصول على تاريخ النهاية كـ DateTime
  DateTime get endDateTime => 
      DateTime.fromMillisecondsSinceEpoch(endTime * 1000);

  /// الحصول على مدة الحدث بالدقائق
  int get eventDuration {
    if (duration != null) return duration!;
    return ((endTime - startTime) / 60).round();
  }

  /// التحقق من كون الحدث جاري حالياً
  bool get isOngoing {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= startTime && now <= endTime;
  }

  /// التحقق من كون الحدث في المستقبل
  bool get isFuture {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return startTime > now;
  }

  /// التحقق من كون الحدث في الماضي
  bool get isPast {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return endTime < now;
  }

  @override
  String toString() {
    return 'CalendarEvent(id: $id, title: $title, startTime: ${startDateTime})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalendarEvent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء حدث تقويم
class CreateCalendarEventRequest {
  final String title;
  final String? description;
  final int startTime;
  final int endTime;
  final bool allDay;
  final String? location;
  final String? color;
  final int? taskId;
  final String? recurrenceRule;

  const CreateCalendarEventRequest({
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.allDay = false,
    this.location,
    this.color,
    this.taskId,
    this.recurrenceRule,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'startTime': startTime,
      'endTime': endTime,
      'allDay': allDay,
      'location': location,
      'color': color,
      'taskId': taskId,
      'recurrenceRule': recurrenceRule,
    };
  }
}

/// نموذج طلب تحديث حدث تقويم
class UpdateCalendarEventRequest {
  final String? title;
  final String? description;
  final int? startTime;
  final int? endTime;
  final bool? allDay;
  final String? location;
  final String? color;
  final int? taskId;
  final String? recurrenceRule;

  const UpdateCalendarEventRequest({
    this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.allDay,
    this.location,
    this.color,
    this.taskId,
    this.recurrenceRule,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (description != null) json['description'] = description;
    if (startTime != null) json['startTime'] = startTime;
    if (endTime != null) json['endTime'] = endTime;
    if (allDay != null) json['allDay'] = allDay;
    if (location != null) json['location'] = location;
    if (color != null) json['color'] = color;
    if (taskId != null) json['taskId'] = taskId;
    if (recurrenceRule != null) json['recurrenceRule'] = recurrenceRule;
    return json;
  }
}

/// نموذج عرض التقويم
class CalendarView {
  final DateTime startDate;
  final DateTime endDate;
  final List<CalendarEvent> events;
  final List<Task> tasks;

  const CalendarView({
    required this.startDate,
    required this.endDate,
    required this.events,
    required this.tasks,
  });

  factory CalendarView.fromJson(Map<String, dynamic> json) {
    return CalendarView(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      events: (json['events'] as List)
          .map((e) => CalendarEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      tasks: (json['tasks'] as List)
          .map((t) => Task.fromJson(t as Map<String, dynamic>))
          .toList(),
    );
  }

  /// الحصول على الأحداث في يوم معين
  List<CalendarEvent> getEventsForDate(DateTime date) {
    return events.where((event) {
      final eventDate = event.startDateTime;
      return eventDate.year == date.year &&
             eventDate.month == date.month &&
             eventDate.day == date.day;
    }).toList();
  }

  /// الحصول على المهام المستحقة في يوم معين
  List<Task> getTasksForDate(DateTime date) {
    return tasks.where((task) {
      if (task.dueDate == null) return false;
      final taskDate = task.dueDate!;
      return taskDate.year == date.year &&
             taskDate.month == date.month &&
             taskDate.day == date.day;
    }).toList();
  }

  /// التحقق من وجود أحداث في يوم معين
  bool hasEventsOnDate(DateTime date) {
    return getEventsForDate(date).isNotEmpty;
  }

  /// التحقق من وجود مهام في يوم معين
  bool hasTasksOnDate(DateTime date) {
    return getTasksForDate(date).isNotEmpty;
  }
}
