import 'user_model.dart';
import 'task_models.dart';

/// نموذج تعليق المهمة
class TaskComment {
  final int id;
  final int taskId;
  final int userId;
  final String content;
  final int? parentCommentId;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isEdited;

  // Navigation properties
  final Task? task;
  final User? user;
  final TaskComment? parentComment;
  final List<TaskComment>? replies;

  const TaskComment({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.content,
    this.parentCommentId,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isEdited = false,
    this.task,
    this.user,
    this.parentComment,
    this.replies,
  });

  factory TaskComment.fromJson(Map<String, dynamic> json) {
    return TaskComment(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      content: json['content'] as String,
      parentCommentId: json['parentCommentId'] as int?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isEdited: json['isEdited'] as bool? ?? false,
      task: json['task'] != null 
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      parentComment: json['parentComment'] != null 
          ? TaskComment.fromJson(json['parentComment'] as Map<String, dynamic>)
          : null,
      replies: json['inverseParentComment'] != null 
          ? (json['inverseParentComment'] as List)
              .map((r) => TaskComment.fromJson(r as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'content': content,
      'parentCommentId': parentCommentId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isEdited': isEdited,
    };
  }

  TaskComment copyWith({
    int? id,
    int? taskId,
    int? userId,
    String? content,
    int? parentCommentId,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    bool? isEdited,
    Task? task,
    User? user,
    TaskComment? parentComment,
    List<TaskComment>? replies,
  }) {
    return TaskComment(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isEdited: isEdited ?? this.isEdited,
      task: task ?? this.task,
      user: user ?? this.user,
      parentComment: parentComment ?? this.parentComment,
      replies: replies ?? this.replies,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// التحقق من كون التعليق رد على تعليق آخر
  bool get isReply => parentCommentId != null;

  /// التحقق من وجود ردود على التعليق
  bool get hasReplies => replies != null && replies!.isNotEmpty;

  /// عدد الردود
  int get replyCount => replies?.length ?? 0;

  @override
  String toString() {
    return 'TaskComment(id: $id, taskId: $taskId, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج تاريخ المهمة
class TaskHistory {
  final int id;
  final int taskId;
  final int userId;
  final String action; // created, updated, assigned, completed, etc.
  final String? oldValue;
  final String? newValue;
  final String? fieldName;
  final String? description;
  final int createdAt;

  // Navigation properties
  final Task? task;
  final User? user;

  const TaskHistory({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.action,
    this.oldValue,
    this.newValue,
    this.fieldName,
    this.description,
    required this.createdAt,
    this.task,
    this.user,
  });

  factory TaskHistory.fromJson(Map<String, dynamic> json) {
    return TaskHistory(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      action: json['action'] as String,
      oldValue: json['oldValue'] as String?,
      newValue: json['newValue'] as String?,
      fieldName: json['fieldName'] as String?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      task: json['task'] != null 
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'action': action,
      'oldValue': oldValue,
      'newValue': newValue,
      'fieldName': fieldName,
      'description': description,
      'createdAt': createdAt,
    };
  }

  TaskHistory copyWith({
    int? id,
    int? taskId,
    int? userId,
    String? action,
    String? oldValue,
    String? newValue,
    String? fieldName,
    String? description,
    int? createdAt,
    Task? task,
    User? user,
  }) {
    return TaskHistory(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      action: action ?? this.action,
      oldValue: oldValue ?? this.oldValue,
      newValue: newValue ?? this.newValue,
      fieldName: fieldName ?? this.fieldName,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      task: task ?? this.task,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على وصف الإجراء باللغة العربية
  String get actionDescription {
    switch (action.toLowerCase()) {
      case 'created': return 'تم إنشاء المهمة';
      case 'updated': return 'تم تحديث المهمة';
      case 'assigned': return 'تم تعيين المهمة';
      case 'completed': return 'تم إكمال المهمة';
      case 'status_changed': return 'تم تغيير حالة المهمة';
      case 'priority_changed': return 'تم تغيير أولوية المهمة';
      case 'due_date_changed': return 'تم تغيير تاريخ الاستحقاق';
      case 'comment_added': return 'تم إضافة تعليق';
      case 'attachment_added': return 'تم إضافة مرفق';
      case 'deleted': return 'تم حذف المهمة';
      default: return action;
    }
  }

  @override
  String toString() {
    return 'TaskHistory(id: $id, action: $action, createdAt: $createdAtDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskHistory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إضافة تعليق
class AddTaskCommentRequest {
  final int taskId;
  final String content;
  final int? parentCommentId;

  const AddTaskCommentRequest({
    required this.taskId,
    required this.content,
    this.parentCommentId,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'content': content,
      'parentCommentId': parentCommentId,
    };
  }
}

/// نموذج طلب تحديث تعليق
class UpdateTaskCommentRequest {
  final String content;

  const UpdateTaskCommentRequest({
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
    };
  }
}
