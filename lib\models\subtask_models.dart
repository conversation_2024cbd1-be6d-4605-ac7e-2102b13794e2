import 'task_models.dart';

/// نموذج المهمة الفرعية - متطابق مع ASP.NET Core API
class Subtask {
  final int id;
  final int taskId;
  final String title;
  final bool isCompleted;
  final int createdAt;
  final int? completedAt;

  // Navigation properties
  final Task? task;

  const Subtask({
    required this.id,
    required this.taskId,
    required this.title,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
    this.task,
  });

  factory Subtask.fromJson(Map<String, dynamic> json) {
    return Subtask(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      title: json['title'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      completedAt: json['completedAt'] as int?,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'title': title,
      'description': description,
      'status': status,
      'priority': priority,
      'assigneeId': assigneeId,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'startDate': startDate,
      'dueDate': dueDate,
      'completedAt': completedAt,
      'estimatedTime': estimatedTime,
      'actualTime': actualTime,
      'completionPercentage': completionPercentage,
      'isDeleted': isDeleted,
    };
  }

  Subtask copyWith({
    int? id,
    int? taskId,
    String? title,
    String? description,
    int? status,
    int? priority,
    int? assigneeId,
    int? createdBy,
    int? createdAt,
    int? startDate,
    int? dueDate,
    int? completedAt,
    int? estimatedTime,
    int? actualTime,
    int? completionPercentage,
    bool? isDeleted,
    Task? task,
    User? assignee,
    User? createdByUser,
  }) {
    return Subtask(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      assigneeId: assigneeId ?? this.assigneeId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      actualTime: actualTime ?? this.actualTime,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      isDeleted: isDeleted ?? this.isDeleted,
      task: task ?? this.task,
      assignee: assignee ?? this.assignee,
      createdByUser: createdByUser ?? this.createdByUser,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime? get startDateDateTime => startDate != null
      ? DateTime.fromMillisecondsSinceEpoch(startDate! * 1000)
      : null;

  /// الحصول على تاريخ الاستحقاق كـ DateTime
  DateTime? get dueDateDateTime => dueDate != null
      ? DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000)
      : null;

  /// الحصول على تاريخ الإكمال كـ DateTime
  DateTime? get completedAtDateTime => completedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(completedAt! * 1000)
      : null;

  /// التحقق من كون المهمة الفرعية مكتملة
  bool get isCompleted => status == 2;

  /// التحقق من كون المهمة الفرعية قيد التنفيذ
  bool get isInProgress => status == 1;

  /// التحقق من كون المهمة الفرعية معلقة
  bool get isPending => status == 0;

  /// التحقق من تأخر المهمة الفرعية
  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDateDateTime!);
  }

  /// الحصول على اسم الحالة
  String get statusName {
    switch (status) {
      case 0: return 'معلقة';
      case 1: return 'قيد التنفيذ';
      case 2: return 'مكتملة';
      default: return 'غير معروف';
    }
  }

  /// الحصول على اسم الأولوية
  String get priorityName {
    switch (priority) {
      case 1: return 'منخفضة';
      case 2: return 'متوسطة';
      case 3: return 'عالية';
      case 4: return 'عاجلة';
      default: return 'غير معروف';
    }
  }

  @override
  String toString() {
    return 'Subtask(id: $id, title: $title, status: $statusName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subtask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مهمة فرعية
class CreateSubtaskRequest {
  final int taskId;
  final String title;
  final String? description;
  final int? assigneeId;
  final DateTime? startDate;
  final DateTime? dueDate;
  final int priority;
  final int? estimatedTime;

  const CreateSubtaskRequest({
    required this.taskId,
    required this.title,
    this.description,
    this.assigneeId,
    this.startDate,
    this.dueDate,
    this.priority = 2,
    this.estimatedTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'title': title,
      'description': description,
      'assigneeId': assigneeId,
      'startDate': startDate?.millisecondsSinceEpoch ~/ 1000,
      'dueDate': dueDate?.millisecondsSinceEpoch ~/ 1000,
      'priority': priority,
      'estimatedTime': estimatedTime,
    };
  }
}

/// نموذج طلب تحديث مهمة فرعية
class UpdateSubtaskRequest {
  final String? title;
  final String? description;
  final int? status;
  final int? priority;
  final int? assigneeId;
  final DateTime? startDate;
  final DateTime? dueDate;
  final int? completionPercentage;
  final int? actualTime;

  const UpdateSubtaskRequest({
    this.title,
    this.description,
    this.status,
    this.priority,
    this.assigneeId,
    this.startDate,
    this.dueDate,
    this.completionPercentage,
    this.actualTime,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (title != null) data['title'] = title;
    if (description != null) data['description'] = description;
    if (status != null) data['status'] = status;
    if (priority != null) data['priority'] = priority;
    if (assigneeId != null) data['assigneeId'] = assigneeId;
    if (startDate != null) data['startDate'] = startDate!.millisecondsSinceEpoch ~/ 1000;
    if (dueDate != null) data['dueDate'] = dueDate!.millisecondsSinceEpoch ~/ 1000;
    if (completionPercentage != null) data['completionPercentage'] = completionPercentage;
    if (actualTime != null) data['actualTime'] = actualTime;
    return data;
  }
}
