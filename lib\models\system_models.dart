import 'user_model.dart';

/// أنواع سجلات النظام
enum SystemLogType {
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  error('error', 'خطأ'),
  security('security', 'أمان'),
  audit('audit', 'مراجعة');

  const SystemLogType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static SystemLogType fromValue(String value) {
    return SystemLogType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SystemLogType.info,
    );
  }
}

/// نموذج سجل النظام
class SystemLog {
  final int id;
  final SystemLogType logType;
  final String message;
  final String? details;
  final int? userId;
  final String? ipAddress;
  final String? userAgent;
  final int timestamp;

  // Navigation properties
  final User? user;

  const SystemLog({
    required this.id,
    required this.logType,
    required this.message,
    this.details,
    this.userId,
    this.ipAddress,
    this.userAgent,
    required this.timestamp,
    this.user,
  });

  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as int,
      logType: SystemLogType.fromValue(json['logType'] as String),
      message: json['message'] as String,
      details: json['details'] as String?,
      userId: json['userId'] as int?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      timestamp: json['timestamp'] as int,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'logType': logType.value,
      'message': message,
      'details': details,
      'userId': userId,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'timestamp': timestamp,
    };
  }

  /// الحصول على التاريخ كـ DateTime
  DateTime get timestampDateTime => 
      DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
}

/// نموذج إعدادات النظام
class SystemSetting {
  final int id;
  final String key;
  final String value;
  final String? description;
  final String? category;
  final bool isPublic;
  final int createdAt;
  final int? updatedAt;

  const SystemSetting({
    required this.id,
    required this.key,
    required this.value,
    this.description,
    this.category,
    this.isPublic = false,
    required this.createdAt,
    this.updatedAt,
  });

  factory SystemSetting.fromJson(Map<String, dynamic> json) {
    return SystemSetting(
      id: json['id'] as int,
      key: json['key'] as String,
      value: json['value'] as String,
      description: json['description'] as String?,
      category: json['category'] as String?,
      isPublic: json['isPublic'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'description': description,
      'category': category,
      'isPublic': isPublic,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}

/// نموذج النسخة الاحتياطية
class Backup {
  final int id;
  final String fileName;
  final String filePath;
  final int fileSize;
  final int createdBy;
  final int createdAt;
  final String? description;
  final bool isAutoBackup;
  final bool isRestored;
  final int? restoredAt;
  final int? restoredBy;

  // Navigation properties
  final User? createdByUser;
  final User? restoredByUser;

  const Backup({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.createdBy,
    required this.createdAt,
    this.description,
    this.isAutoBackup = false,
    this.isRestored = false,
    this.restoredAt,
    this.restoredBy,
    this.createdByUser,
    this.restoredByUser,
  });

  factory Backup.fromJson(Map<String, dynamic> json) {
    return Backup(
      id: json['id'] as int,
      fileName: json['fileName'] as String,
      filePath: json['filePath'] as String,
      fileSize: json['fileSize'] as int,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      description: json['description'] as String?,
      isAutoBackup: json['isAutoBackup'] as bool? ?? false,
      isRestored: json['isRestored'] as bool? ?? false,
      restoredAt: json['restoredAt'] as int?,
      restoredBy: json['restoredBy'] as int?,
      createdByUser: json['createdByNavigation'] != null 
          ? User.fromJson(json['createdByNavigation'] as Map<String, dynamic>)
          : null,
      restoredByUser: json['restoredByNavigation'] != null 
          ? User.fromJson(json['restoredByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'description': description,
      'isAutoBackup': isAutoBackup,
      'isRestored': isRestored,
      'restoredAt': restoredAt,
      'restoredBy': restoredBy,
    };
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الاستعادة كـ DateTime
  DateTime? get restoredAtDateTime => restoredAt != null 
      ? DateTime.fromMillisecondsSinceEpoch(restoredAt! * 1000)
      : null;
}

/// نموذج الصلاحيات
class Permission {
  final int id;
  final String name;
  final String? description;
  final String? category;
  final bool isActive;
  final int createdAt;

  const Permission({
    required this.id,
    required this.name,
    this.description,
    this.category,
    this.isActive = true,
    required this.createdAt,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    return Permission(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: json['category'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'isActive': isActive,
      'createdAt': createdAt,
    };
  }
}

/// نموذج صلاحيات المستخدم
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final int? revokedAt;
  final int? revokedBy;
  final bool isActive;

  // Navigation properties
  final User? user;
  final Permission? permission;
  final User? grantedByUser;
  final User? revokedByUser;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.revokedAt,
    this.revokedBy,
    this.isActive = true,
    this.user,
    this.permission,
    this.grantedByUser,
    this.revokedByUser,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as int,
      userId: json['userId'] as int,
      permissionId: json['permissionId'] as int,
      grantedBy: json['grantedBy'] as int,
      grantedAt: json['grantedAt'] as int,
      revokedAt: json['revokedAt'] as int?,
      revokedBy: json['revokedBy'] as int?,
      isActive: json['isActive'] as bool? ?? true,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      permission: json['permission'] != null 
          ? Permission.fromJson(json['permission'] as Map<String, dynamic>)
          : null,
      grantedByUser: json['grantedByNavigation'] != null 
          ? User.fromJson(json['grantedByNavigation'] as Map<String, dynamic>)
          : null,
      revokedByUser: json['revokedByNavigation'] != null 
          ? User.fromJson(json['revokedByNavigation'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'revokedAt': revokedAt,
      'revokedBy': revokedBy,
      'isActive': isActive,
    };
  }
}

/// نموذج طلب إنشاء نسخة احتياطية
class CreateBackupRequest {
  final String? description;
  final bool includeFiles;

  const CreateBackupRequest({
    this.description,
    this.includeFiles = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'includeFiles': includeFiles,
    };
  }
}

/// نموذج طلب تحديث إعدادات النظام
class UpdateSystemSettingsRequest {
  final Map<String, String> settings;

  const UpdateSystemSettingsRequest({
    required this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'settings': settings,
    };
  }
}
