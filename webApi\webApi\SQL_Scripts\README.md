# Database Migration Scripts - تحديث قاعدة البيانات

## نظرة عامة
هذا المجلد يحتوي على سكريبتات SQL لتحديث قاعدة البيانات لتتوافق مع التعديلات التي تم إجراؤها على نماذج الأرشيف.

## الملفات المتوفرة

### 1. `UpdateArchiveModels.sql`
سكريبت تحديث قاعدة البيانات لإضافة الخصائص الجديدة:

**التعديلات المطبقة:**
- **archive_categories**: إضافة `color`, `icon`, `is_active`
- **archive_documents**: إضافة `content`, `created_by`, `created_at`
- **archive_tags**: إضافة `description`, `is_active`
- **archive_document_tags**: إنشاء جدول جديد للربط بين الوثائق والعلامات

### 2. `RollbackArchiveModels.sql`
سكريبت التراجع عن التعديلات (استخدم بحذر!)

## كيفية التطبيق

### الطريقة الأولى: SQL Server Management Studio (SSMS)
1. افتح SQL Server Management Studio
2. اتصل بقاعدة البيانات `databasetasks`
3. افتح ملف `UpdateArchiveModels.sql`
4. نفذ السكريبت

### الطريقة الثانية: سطر الأوامر
```bash
sqlcmd -S .\sqlexpress -d databasetasks -i "UpdateArchiveModels.sql"
```

### الطريقة الثالثة: من خلال Visual Studio
1. افتح SQL Server Object Explorer
2. اتصل بقاعدة البيانات
3. انقر بالزر الأيمن على قاعدة البيانات → New Query
4. انسخ محتوى `UpdateArchiveModels.sql` ونفذه

## التحقق من نجاح التطبيق

بعد تنفيذ السكريبت، تحقق من:

1. **الجداول المحدثة:**
   ```sql
   -- التحقق من archive_categories
   SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_NAME = 'archive_categories';
   
   -- التحقق من archive_documents
   SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_NAME = 'archive_documents';
   
   -- التحقق من archive_tags
   SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_NAME = 'archive_tags';
   
   -- التحقق من الجدول الجديد
   SELECT * FROM INFORMATION_SCHEMA.TABLES 
   WHERE TABLE_NAME = 'archive_document_tags';
   ```

## ملاحظات مهمة

⚠️ **تحذيرات:**
- قم بعمل نسخة احتياطية من قاعدة البيانات قبل التطبيق
- تأكد من عدم وجود تطبيقات تستخدم قاعدة البيانات أثناء التحديث
- اختبر السكريبت على بيئة التطوير أولاً

✅ **مميزات السكريبت:**
- يتحقق من وجود الأعمدة قبل الإضافة (آمن للتنفيذ المتكرر)
- يحدث البيانات الموجودة بقيم افتراضية مناسبة
- يضيف القيود والعلاقات المطلوبة

## استكشاف الأخطاء

إذا واجهت مشاكل:

1. **خطأ في الصلاحيات:**
   - تأكد من أن المستخدم لديه صلاحيات `db_ddladmin`

2. **خطأ في Foreign Key:**
   - تأكد من وجود جدول `users` وأن به بيانات

3. **خطأ في البيانات:**
   - تحقق من وجود بيانات في الجداول الأصلية

## الدعم
في حالة وجود مشاكل، راجع:
- سجلات SQL Server
- رسائل الخطأ في الكونسول
- تأكد من تطابق أسماء الجداول والأعمدة
